#!/usr/bin/env python3
"""
Test script to check if the image embedding model is working.
"""
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from src.services.medical_embedding_service import MedicalEmbeddingService

def test_image_model():
    """Test the image embedding model initialization and functionality."""
    print("=== Testing Image Embedding Model ===")

    # Initialize the embedding service
    print("1. Initializing MedicalEmbeddingService...")
    embedding_service = MedicalEmbeddingService()

    print(f"   Text model name: {embedding_service.text_model_name}")
    print(f"   Image model name: {embedding_service.image_model_name}")
    print(f"   Has text model: {embedding_service.text_model is not None}")
    print(f"   Has image model: {embedding_service.has_image_model}")

    if not embedding_service.has_image_model:
        print("\n❌ Image model is not available!")
        print("This explains why images are not being processed properly.")

        # Try to manually initialize a simple CLIP model
        print("\n2. Trying to manually initialize CLIP model...")
        try:
            from transformers import CLIPProcessor, CLIPModel
            processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
            model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
            print("✅ CLIP model loaded successfully!")

            # Test with a simple image
            print("\n3. Testing CLIP model with a test image...")
            from PIL import Image
            import numpy as np
            import torch

            # Create a simple test image
            test_image = Image.new('RGB', (224, 224), color='red')

            # Process the image
            inputs = processor(images=test_image, return_tensors="pt")
            with torch.no_grad():
                outputs = model(**inputs)

            if hasattr(outputs, 'image_embeds'):
                embedding = outputs.image_embeds.numpy()
                print(f"✅ Generated test embedding with shape: {embedding.shape}")
            else:
                print("❌ Could not extract image embeddings from CLIP model")

        except Exception as e:
            print(f"❌ Failed to load CLIP model: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("\n✅ Image model is available!")

        # Test with a simple image
        print("\n2. Testing image embedding generation...")
        try:
            # Create a proper test image using PIL
            from PIL import Image
            import io

            # Create a simple RGB image
            test_image = Image.new('RGB', (224, 224), color='red')

            # Convert to bytes
            img_byte_arr = io.BytesIO()
            test_image.save(img_byte_arr, format='JPEG')
            jpeg_data = img_byte_arr.getvalue()

            print(f"Created test JPEG with {len(jpeg_data)} bytes")

            embedding = embedding_service.get_image_embedding(jpeg_data)
            if embedding is not None:
                print(f"✅ Generated image embedding with shape: {embedding.shape}")
            else:
                print("❌ Failed to generate image embedding")

        except Exception as e:
            print(f"❌ Error testing image embedding: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_image_model()
