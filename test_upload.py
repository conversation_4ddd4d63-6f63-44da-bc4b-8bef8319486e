#!/usr/bin/env python3
"""
Test script to upload a file and check if it appears in the document list.
"""
import requests
import json
import os

# Configuration
BASE_URL = "http://localhost:5000"
LOGIN_URL = f"{BASE_URL}/api/auth/login"
UPLOAD_URL = f"{BASE_URL}/api/documents/upload"
LIST_URL = f"{BASE_URL}/api/documents/list"

# Test credentials (use the test users from auth_service.py)
TEST_USERNAME = "gabriel"
TEST_PASSWORD = "gabriel123"

def login():
    """Login and get JWT token."""
    login_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }

    response = requests.post(LOGIN_URL, json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data.get("token")  # The response field is "token", not "access_token"
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def upload_test_image(token):
    """Upload a test image."""
    # Create a simple test image file
    test_image_path = "test_image.jpg"

    # Create a minimal JPEG file (just for testing)
    # This is a minimal valid JPEG header
    jpeg_data = bytes([
        0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
        0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
        0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
        0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
        0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
        0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
        0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xD9
    ])

    with open(test_image_path, 'wb') as f:
        f.write(jpeg_data)

    try:
        # Upload the file
        with open(test_image_path, 'rb') as f:
            files = {'file': ('test_image.jpg', f, 'image/jpeg')}
            headers = {'Authorization': f'Bearer {token}'}

            response = requests.post(UPLOAD_URL, files=files, headers=headers)
            print(f"Upload response: {response.status_code} - {response.text}")
            return response.status_code == 200
    finally:
        # Clean up test file
        if os.path.exists(test_image_path):
            os.remove(test_image_path)

def list_documents(token):
    """List documents."""
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(LIST_URL, headers=headers)
    print(f"List response: {response.status_code} - {response.text}")
    return response.json() if response.status_code == 200 else None

def main():
    """Main test function."""
    print("=== Testing Document Upload ===")

    # Login
    print("1. Logging in...")
    token = login()
    if not token:
        print("Failed to login. Please check credentials and backend status.")
        return

    print(f"Login successful, token: {token[:20]}...")

    # List documents before upload
    print("\n2. Listing documents before upload...")
    docs_before = list_documents(token)
    print(f"Documents before: {docs_before}")

    # Upload test image
    print("\n3. Uploading test image...")
    upload_success = upload_test_image(token)

    if upload_success:
        print("Upload successful!")

        # List documents after upload
        print("\n4. Listing documents after upload...")
        docs_after = list_documents(token)
        print(f"Documents after: {docs_after}")

        # Compare
        if docs_after and len(docs_after.get('documents', [])) > len(docs_before.get('documents', []) if docs_before else []):
            print("\n✅ SUCCESS: Document appears in list after upload!")
        else:
            print("\n❌ ISSUE: Document does not appear in list after upload!")
    else:
        print("Upload failed!")

if __name__ == "__main__":
    main()
