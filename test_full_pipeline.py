#!/usr/bin/env python3
"""
Test the full image processing pipeline step by step.
"""
import sys
import os
import io
from PIL import Image

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from src.services.document_service import DocumentService
from src.utils.document_processor import DocumentProcessor

def test_full_pipeline():
    """Test the complete image processing pipeline."""
    print("=== Testing Full Image Processing Pipeline ===")
    
    # Create a test image
    print("1. Creating test image...")
    test_image = Image.new('RGB', (224, 224), color='red')
    img_byte_arr = io.BytesIO()
    test_image.save(img_byte_arr, format='JPEG')
    image_bytes = img_byte_arr.getvalue()
    print(f"Created test image: {len(image_bytes)} bytes")
    
    # Test document processor
    print("\n2. Testing DocumentProcessor...")
    processor = DocumentProcessor()
    chunks = processor._process_image_bytes(image_bytes, "test_medical_image.jpg")
    print(f"Generated {len(chunks)} chunks")
    for i, chunk in enumerate(chunks):
        print(f"Chunk {i+1}:")
        print(f"  Content: {chunk['content'][:100]}...")
        print(f"  Metadata keys: {list(chunk['metadata'].keys())}")
        print(f"  Content type: {chunk['metadata'].get('content_type')}")
        print(f"  Has image data: {'image_data' in chunk['metadata']}")
    
    # Test document service
    print("\n3. Testing DocumentService...")
    try:
        document_service = DocumentService()
        
        # Save test image to file temporarily
        test_file_path = "temp_test_image.jpg"
        with open(test_file_path, 'wb') as f:
            f.write(image_bytes)
        
        print(f"Saved test image to: {test_file_path}")
        
        # Process and store
        print("Processing and storing document...")
        document_id = document_service.process_and_store_document(
            test_file_path,
            "test_medical_image.jpg",
            user_role="patient",
            user_id="test_user"
        )
        
        print(f"Document ID returned: {document_id}")
        
        # Check if it was stored
        print("\n4. Checking if document was stored...")
        documents = document_service.get_documents_by_user("test_user")
        print(f"Found {len(documents)} documents for test_user")
        
        for doc in documents:
            print(f"  - {doc.get('filename')} (type: {doc.get('metadata', {}).get('content_type')})")
        
        # Clean up
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"Cleaned up: {test_file_path}")
            
    except Exception as e:
        print(f"Error in document service test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_pipeline()
