#!/usr/bin/env python3
"""
Debug script to investigate image upload issue.
"""
import requests
import json
import os
import time

# Configuration
BASE_URL = "http://localhost:5000"
LOGIN_URL = f"{BASE_URL}/api/auth/login"
UPLOAD_URL = f"{BASE_URL}/api/documents/upload"
LIST_URL = f"{BASE_URL}/api/documents/list"

# Test credentials
TEST_USERNAME = "gabriel"
TEST_PASSWORD = "gabriel123"

def login():
    """Login and get JWT token."""
    login_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }

    response = requests.post(LOGIN_URL, json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data.get("token")
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def upload_real_image(token, image_path):
    """Upload a real image file."""
    if not os.path.exists(image_path):
        print(f"Image file not found: {image_path}")
        return False

    try:
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
            headers = {'Authorization': f'Bearer {token}'}

            print(f"Uploading {image_path}...")
            response = requests.post(UPLOAD_URL, files=files, headers=headers)
            print(f"Upload response: {response.status_code}")
            print(f"Response body: {response.text}")

            if response.status_code == 200:
                data = response.json()
                print(f"Document ID: {data.get('document_id')}")
                return True
            else:
                print(f"Upload failed: {response.status_code} - {response.text}")
                return False
    except Exception as e:
        print(f"Error uploading file: {e}")
        return False

def list_documents(token):
    """List documents with detailed output."""
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(LIST_URL, headers=headers)
    print(f"List response status: {response.status_code}")

    if response.status_code == 200:
        data = response.json()
        documents = data.get('documents', [])
        print(f"Found {len(documents)} documents:")
        for i, doc in enumerate(documents):
            print(f"  {i+1}. {doc.get('filename')} (ID: {doc.get('id')})")
            print(f"     Content type: {doc.get('metadata', {}).get('content_type')}")
            print(f"     User ID: {doc.get('metadata', {}).get('user_id')}")
        return data
    else:
        print(f"List failed: {response.status_code} - {response.text}")
        return None

def check_qdrant_directly():
    """Check Qdrant directly to see if the image was stored."""
    try:
        from qdrant_client import QdrantClient
        from qdrant_client.http import models

        # Connect to local Qdrant
        client = QdrantClient(path="./backend/qdrant_data")

        # Get all points
        scroll_result = client.scroll(
            collection_name="health_documents",
            limit=100,
            with_payload=True,
            with_vectors=False
        )

        points = scroll_result[0]
        print(f"\nDirect Qdrant check - Found {len(points)} total points:")

        image_points = []
        for point in points:
            metadata = point.payload.get("metadata", {})
            content_type = metadata.get("content_type")
            source = metadata.get("source", "")
            user_id = metadata.get("user_id", "")

            print(f"  Point {point.id}: {source} (type: {content_type}, user: {user_id})")

            if content_type == "image" or source.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_points.append(point)

        print(f"\nFound {len(image_points)} image points in Qdrant")
        for point in image_points:
            metadata = point.payload.get("metadata", {})
            print(f"  Image: {metadata.get('source')} (user: {metadata.get('user_id')})")

        # Also check for gabriel user specifically
        gabriel_points = []
        for point in points:
            metadata = point.payload.get("metadata", {})
            user_id = metadata.get("user_id", "")
            if user_id == "gabriel":
                gabriel_points.append(point)

        print(f"\nFound {len(gabriel_points)} total points for user 'gabriel':")
        for point in gabriel_points:
            metadata = point.payload.get("metadata", {})
            content_type = metadata.get("content_type")
            source = metadata.get("source", "")
            print(f"  {source} (type: {content_type})")

    except Exception as e:
        print(f"Error checking Qdrant directly: {e}")

def main():
    """Main debug function."""
    print("=== Debug Image Upload Issue ===")

    # Login
    print("1. Logging in...")
    token = login()
    if not token:
        print("Failed to login")
        return

    print(f"Login successful")

    # List documents before
    print("\n2. Listing documents before upload...")
    docs_before = list_documents(token)

    # Try to upload IMG_4609.jpg if it exists
    image_path = "IMG_4609.jpg"
    if not os.path.exists(image_path):
        print(f"\n{image_path} not found, creating a test image...")
        # Create a minimal JPEG for testing
        jpeg_data = bytes([
            0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
            0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
            0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
            0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
            0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
            0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
            0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
            0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xD9
        ])

        with open(image_path, 'wb') as f:
            f.write(jpeg_data)
        print(f"Created test image: {image_path}")

    # Upload image
    print(f"\n3. Uploading {image_path}...")
    upload_success = upload_real_image(token, image_path)

    if upload_success:
        print("Upload reported success!")

        # Wait a moment for processing
        print("Waiting 2 seconds for processing...")
        time.sleep(2)

        # List documents after
        print("\n4. Listing documents after upload...")
        docs_after = list_documents(token)

        # Check Qdrant directly
        print("\n5. Checking Qdrant directly...")
        check_qdrant_directly()

    else:
        print("Upload failed!")

    # Clean up test file if we created it
    if os.path.exists(image_path) and image_path == "IMG_4609.jpg":
        try:
            os.remove(image_path)
            print(f"\nCleaned up test file: {image_path}")
        except:
            pass

if __name__ == "__main__":
    main()
